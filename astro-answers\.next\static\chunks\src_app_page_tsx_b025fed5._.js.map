{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AstroAnswers/astro-answers/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { initializeApp } from 'firebase/app';\nimport {\n  getAuth,\n  createUserWithEmailAndPassword,\n  signInWithEmailAndPassword,\n  signOut,\n  onAuthStateChanged,\n  signInWithCustomToken,\n  signInAnonymously,\n  User\n} from 'firebase/auth';\nimport {\n  getFirestore,\n  doc,\n  setDoc,\n  getDoc\n} from 'firebase/firestore';\n\n// Firebase configuration - using global variables as specified\ndeclare global {\n  var __app_id: string;\n  var __firebase_config: {\n    apiKey: string;\n    authDomain: string;\n    projectId: string;\n    storageBucket: string;\n    messagingSenderId: string;\n    appId: string;\n  };\n  var __initial_auth_token: string;\n}\n\n// Mock astrology book data for demonstration (would come from backend in real app)\nconst ASTROLOGY_BOOK_DATA = [\n  \"The position of planets at birth influences personality traits and life patterns.\",\n  \"Mercury retrograde affects communication, technology, and travel plans.\",\n  \"The twelve zodiac signs each have unique characteristics and ruling planets.\",\n  \"Houses in astrology represent different life areas like career, relationships, and health.\",\n  \"Aspects between planets create harmonious or challenging energy patterns.\",\n  \"The moon phases influence emotions and intuitive cycles throughout the month.\",\n  \"Rising signs determine how others perceive you and your approach to life.\",\n  \"Venus governs love, beauty, and material pleasures in astrological interpretation.\",\n  \"Saturn represents discipline, responsibility, and life lessons through challenges.\",\n  \"Jupiter brings expansion, luck, and opportunities for growth and learning.\"\n];\n\n// Initialize Firebase\nconst firebaseConfig = typeof window !== 'undefined' && window.__firebase_config ?\n  window.__firebase_config : {\n    // Fallback config for development\n    apiKey: \"demo-api-key\",\n    authDomain: \"demo-project.firebaseapp.com\",\n    projectId: \"demo-project\",\n    storageBucket: \"demo-project.appspot.com\",\n    messagingSenderId: \"123456789\",\n    appId: \"demo-app-id\"\n  };\n\nconst app = initializeApp(firebaseConfig);\nconst auth = getAuth(app);\nconst db = getFirestore(app);\n\n// Types for better TypeScript support\ninterface Message {\n  id: string;\n  text: string;\n  isUser: boolean;\n  timestamp: Date;\n}\n\ninterface AuthFormData {\n  email: string;\n  password: string;\n}\n\ninterface ErrorState {\n  auth: string;\n  api: string;\n}\n\nexport default function AstrologyQAApp() {\n  // Authentication state\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isLogin, setIsLogin] = useState(true);\n\n  // Form state\n  const [formData, setFormData] = useState<AuthFormData>({ email: '', password: '' });\n  const [errors, setErrors] = useState<ErrorState>({ auth: '', api: '' });\n\n  // Chat state\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [currentQuestion, setCurrentQuestion] = useState('');\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  // Refs for auto-scrolling\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const questionInputRef = useRef<HTMLTextAreaElement>(null);\n\n  // Initialize Firebase auth and handle initial authentication\n  useEffect(() => {\n    const initializeAuth = async () => {\n      try {\n        // Check for initial auth token\n        const initialToken = typeof window !== 'undefined' && window.__initial_auth_token;\n\n        if (initialToken) {\n          await signInWithCustomToken(auth, initialToken);\n        } else {\n          // Sign in anonymously for demo purposes\n          await signInAnonymously(auth);\n        }\n      } catch (error) {\n        console.error('Initial auth failed:', error);\n      }\n    };\n\n    // Set up auth state listener\n    const unsubscribe = onAuthStateChanged(auth, (user) => {\n      setUser(user);\n      setIsLoading(false);\n\n      // Store user data in Firestore when authenticated\n      if (user) {\n        storeUserData(user);\n      }\n    });\n\n    initializeAuth();\n\n    return () => unsubscribe();\n  }, []);\n\n  // Auto-scroll to bottom of messages\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [messages]);\n\n  // Store user data in Firestore\n  const storeUserData = async (user: User) => {\n    try {\n      const userRef = doc(db, 'users', user.uid);\n      const userSnap = await getDoc(userRef);\n\n      if (!userSnap.exists()) {\n        await setDoc(userRef, {\n          email: user.email,\n          createdAt: new Date(),\n          lastLogin: new Date()\n        });\n      } else {\n        await setDoc(userRef, {\n          lastLogin: new Date()\n        }, { merge: true });\n      }\n    } catch (error) {\n      console.error('Error storing user data:', error);\n    }\n  };\n\n  // Handle user registration\n  const handleRegister = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setErrors({ auth: '', api: '' });\n\n    if (!formData.email || !formData.password) {\n      setErrors(prev => ({ ...prev, auth: 'Please fill in all fields' }));\n      return;\n    }\n\n    if (formData.password.length < 6) {\n      setErrors(prev => ({ ...prev, auth: 'Password must be at least 6 characters' }));\n      return;\n    }\n\n    try {\n      await createUserWithEmailAndPassword(auth, formData.email, formData.password);\n      setFormData({ email: '', password: '' });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Registration failed';\n      setErrors(prev => ({ ...prev, auth: errorMessage }));\n    }\n  };\n\n  // Handle user login\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setErrors({ auth: '', api: '' });\n\n    if (!formData.email || !formData.password) {\n      setErrors(prev => ({ ...prev, auth: 'Please fill in all fields' }));\n      return;\n    }\n\n    try {\n      await signInWithEmailAndPassword(auth, formData.email, formData.password);\n      setFormData({ email: '', password: '' });\n    } catch (error) {\n      console.error('Login error:', error);\n      setErrors(prev => ({ ...prev, auth: 'Invalid email or password' }));\n    }\n  };\n\n  // Handle logout\n  const handleLogout = async () => {\n    try {\n      await signOut(auth);\n      setMessages([]);\n      setCurrentQuestion('');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  // Simulate AI response generation (would call backend API in real app)\n  const generateAIResponse = async (question: string): Promise<string> => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 2000));\n\n    // Find relevant context from our mock astrology book data\n    const relevantContext = ASTROLOGY_BOOK_DATA.filter(snippet =>\n      question.toLowerCase().split(' ').some(word =>\n        snippet.toLowerCase().includes(word) && word.length > 3\n      )\n    );\n\n    // Simulate different types of responses based on question content\n    const questionLower = question.toLowerCase();\n\n    if (questionLower.includes('mercury') && questionLower.includes('retrograde')) {\n      return \"Mercury retrograde occurs 3-4 times per year when Mercury appears to move backward in its orbit from Earth's perspective. During these periods, communication, technology, and travel may experience disruptions. It's a time for reflection, reviewing past decisions, and being extra careful with contracts and important communications. The effects typically last about 3 weeks.\";\n    }\n\n    if (questionLower.includes('zodiac') || questionLower.includes('sign')) {\n      return \"The twelve zodiac signs are divided into four elements: Fire (Aries, Leo, Sagittarius), Earth (Taurus, Virgo, Capricorn), Air (Gemini, Libra, Aquarius), and Water (Cancer, Scorpio, Pisces). Each sign has unique characteristics influenced by its ruling planet and element. Your sun sign represents your core identity, while your moon sign reflects your emotional nature, and your rising sign shows how others perceive you.\";\n    }\n\n    if (questionLower.includes('house') || questionLower.includes('houses')) {\n      return \"The twelve astrological houses represent different life areas. The 1st house governs identity and appearance, the 2nd house rules money and possessions, the 3rd house covers communication and siblings, the 4th house represents home and family, and so on. The planets' positions in these houses at your birth time influence how their energies manifest in specific life areas.\";\n    }\n\n    if (questionLower.includes('planet') || questionLower.includes('planets')) {\n      return \"Each planet in astrology governs specific life themes: Sun (identity, ego), Moon (emotions, intuition), Mercury (communication, thinking), Venus (love, beauty), Mars (action, desire), Jupiter (expansion, luck), Saturn (discipline, lessons), Uranus (innovation, rebellion), Neptune (dreams, spirituality), and Pluto (transformation, power). Their positions and aspects create your unique astrological blueprint.\";\n    }\n\n    // Default response using available context\n    if (relevantContext.length > 0) {\n      return `Based on astrological principles, ${relevantContext[0]} ${relevantContext.length > 1 ? 'Additionally, ' + relevantContext[1] : ''} This information can help provide insight into your question about ${question.toLowerCase()}.`;\n    }\n\n    return `Thank you for your astrology question about \"${question}\". While I don't have specific information about this topic in my current knowledge base, I'd recommend consulting with a professional astrologer for personalized insights. In general, astrology offers guidance through the study of planetary positions and their influences on human affairs and natural phenomena.`;\n  };\n\n  // Handle sending a question to the AI\n  const handleSendQuestion = async () => {\n    if (!currentQuestion.trim() || isProcessing) return;\n\n    const questionText = currentQuestion.trim();\n    const questionId = Date.now().toString();\n\n    // Add user message to chat\n    const userMessage: Message = {\n      id: questionId,\n      text: questionText,\n      isUser: true,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setCurrentQuestion('');\n    setIsProcessing(true);\n    setErrors(prev => ({ ...prev, api: '' }));\n\n    try {\n      // In a real app, this would be a fetch call to your backend API\n      // const response = await fetch('/api/ask-ai', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify({\n      //     question: questionText,\n      //     userId: user?.uid,\n      //     context: ASTROLOGY_BOOK_DATA // This would be handled by backend\n      //   })\n      // });\n\n      const aiResponse = await generateAIResponse(questionText);\n\n      // Add AI response to chat\n      const aiMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        text: aiResponse,\n        isUser: false,\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, aiMessage]);\n\n    } catch (error) {\n      console.error('Error getting AI response:', error);\n      setErrors(prev => ({ ...prev, api: 'Failed to get response. Please try again.' }));\n    } finally {\n      setIsProcessing(false);\n      // Focus back on input after response\n      setTimeout(() => questionInputRef.current?.focus(), 100);\n    }\n  };\n\n  // Handle Enter key press in textarea\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendQuestion();\n    }\n  };\n\n  // Handle form input changes\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n    // Clear auth errors when user starts typing\n    if (errors.auth) {\n      setErrors(prev => ({ ...prev, auth: '' }));\n    }\n  };\n\n  // Loading screen component\n  const LoadingScreen = () => (\n    <div className=\"min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-white mx-auto mb-4\"></div>\n        <p className=\"text-white text-lg\">Connecting to the cosmos...</p>\n      </div>\n    </div>\n  );\n\n  // Authentication form component\n  const AuthForm = () => (\n    <div className=\"min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center p-4\">\n      <div className=\"bg-white/10 backdrop-blur-md rounded-2xl p-8 w-full max-w-md border border-white/20\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <div className=\"text-4xl mb-2\">🌟</div>\n          <h1 className=\"text-3xl font-bold text-white mb-2\">AstroAnswers</h1>\n          <p className=\"text-purple-200\">Your AI-powered astrology guide</p>\n        </div>\n\n        {/* Form */}\n        <form onSubmit={isLogin ? handleLogin : handleRegister} className=\"space-y-6\">\n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-purple-200 mb-2\">\n              Email Address\n            </label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleInputChange}\n              className=\"w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all\"\n              placeholder=\"Enter your email\"\n              required\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"password\" className=\"block text-sm font-medium text-purple-200 mb-2\">\n              Password\n            </label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleInputChange}\n              className=\"w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all\"\n              placeholder=\"Enter your password\"\n              required\n            />\n          </div>\n\n          {/* Error message */}\n          {errors.auth && (\n            <div className=\"bg-red-500/20 border border-red-500/50 rounded-lg p-3 text-red-200 text-sm\">\n              {errors.auth}\n            </div>\n          )}\n\n          {/* Submit button */}\n          <button\n            type=\"submit\"\n            className=\"w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-transparent\"\n          >\n            {isLogin ? 'Sign In' : 'Create Account'}\n          </button>\n\n          {/* Toggle between login/register */}\n          <div className=\"text-center\">\n            <button\n              type=\"button\"\n              onClick={() => {\n                setIsLogin(!isLogin);\n                setErrors({ auth: '', api: '' });\n                setFormData({ email: '', password: '' });\n              }}\n              className=\"text-purple-300 hover:text-white transition-colors underline\"\n            >\n              {isLogin ? \"Don't have an account? Sign up\" : \"Already have an account? Sign in\"}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n\n  // Message bubble component\n  const MessageBubble = ({ message }: { message: Message }) => (\n    <div className={`flex ${message.isUser ? 'justify-end' : 'justify-start'} mb-4`}>\n      <div className={`max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl rounded-2xl px-4 py-3 ${\n        message.isUser\n          ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white ml-4'\n          : 'bg-white/10 backdrop-blur-sm text-white mr-4 border border-white/20'\n      }`}>\n        <p className=\"text-sm leading-relaxed whitespace-pre-wrap\">{message.text}</p>\n        <p className={`text-xs mt-2 ${message.isUser ? 'text-purple-200' : 'text-purple-300'}`}>\n          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n        </p>\n      </div>\n    </div>\n  );\n\n  // Main chat interface component\n  const ChatInterface = () => (\n    <div className=\"min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex flex-col\">\n      {/* Header */}\n      <header className=\"bg-white/10 backdrop-blur-md border-b border-white/20 p-4\">\n        <div className=\"max-w-4xl mx-auto flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"text-2xl\">🌟</div>\n            <div>\n              <h1 className=\"text-xl font-bold text-white\">AstroAnswers</h1>\n              <p className=\"text-purple-200 text-sm\">AI Astrology Guide</p>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"hidden sm:block text-purple-200 text-sm\">\n              Welcome, {user?.email || 'Cosmic Explorer'}\n            </div>\n            <button\n              onClick={handleLogout}\n              className=\"bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-all border border-white/20 text-sm\"\n            >\n              Sign Out\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Main chat area */}\n      <main className=\"flex-1 flex flex-col max-w-4xl mx-auto w-full p-4\">\n        {/* Messages container */}\n        <div className=\"flex-1 overflow-y-auto mb-4 space-y-2\">\n          {messages.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <div className=\"text-6xl mb-4\">🔮</div>\n              <h2 className=\"text-2xl font-bold text-white mb-2\">Welcome to AstroAnswers</h2>\n              <p className=\"text-purple-200 mb-6 max-w-md mx-auto\">\n                Ask me anything about astrology! I can help you understand zodiac signs, planetary influences,\n                birth charts, and cosmic guidance.\n              </p>\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3 max-w-2xl mx-auto\">\n                {[\n                  \"What does Mercury retrograde mean?\",\n                  \"Tell me about my zodiac sign\",\n                  \"How do astrological houses work?\",\n                  \"What are planetary aspects?\"\n                ].map((suggestion, index) => (\n                  <button\n                    key={index}\n                    onClick={() => setCurrentQuestion(suggestion)}\n                    className=\"bg-white/10 hover:bg-white/20 text-purple-200 hover:text-white px-4 py-3 rounded-lg transition-all border border-white/20 text-sm text-left\"\n                  >\n                    {suggestion}\n                  </button>\n                ))}\n              </div>\n            </div>\n          ) : (\n            <>\n              {messages.map((message) => (\n                <MessageBubble key={message.id} message={message} />\n              ))}\n\n              {/* Loading indicator */}\n              {isProcessing && (\n                <div className=\"flex justify-start mb-4\">\n                  <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl px-4 py-3 mr-4 border border-white/20\">\n                    <div className=\"flex items-center space-x-2\">\n                      <div className=\"flex space-x-1\">\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full animate-bounce\"></div>\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                        <div className=\"w-2 h-2 bg-purple-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                      </div>\n                      <span className=\"text-purple-300 text-sm\">Consulting the stars...</span>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </>\n          )}\n\n          {/* Auto-scroll target */}\n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Error message for API */}\n        {errors.api && (\n          <div className=\"bg-red-500/20 border border-red-500/50 rounded-lg p-3 text-red-200 text-sm mb-4\">\n            {errors.api}\n          </div>\n        )}\n\n        {/* Input area */}\n        <div className=\"bg-white/10 backdrop-blur-md rounded-2xl p-4 border border-white/20\">\n          <div className=\"flex flex-col sm:flex-row gap-3\">\n            <div className=\"flex-1\">\n              <textarea\n                ref={questionInputRef}\n                value={currentQuestion}\n                onChange={(e) => setCurrentQuestion(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"Ask me about astrology... (Press Enter to send, Shift+Enter for new line)\"\n                className=\"w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all resize-none\"\n                rows={3}\n                disabled={isProcessing}\n              />\n            </div>\n            <button\n              onClick={handleSendQuestion}\n              disabled={!currentQuestion.trim() || isProcessing}\n              className=\"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:hover:scale-100 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-transparent self-end\"\n            >\n              {isProcessing ? (\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white\"></div>\n                  <span className=\"hidden sm:inline\">Sending...</span>\n                </div>\n              ) : (\n                <div className=\"flex items-center space-x-2\">\n                  <span>Send</span>\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n                  </svg>\n                </div>\n              )}\n            </button>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n\n  // Main render logic\n  if (isLoading) {\n    return <LoadingScreen />;\n  }\n\n  if (!user) {\n    return <AuthForm />;\n  }\n\n  return <ChatInterface />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAAA;;;AAdA;;;;;AAmCA,mFAAmF;AACnF,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,sBAAsB;AACtB,MAAM,iBAAiB,aAAkB,eAAe,OAAO,iBAAiB,GAC9E,OAAO,iBAAiB,GAAG;IACzB,kCAAkC;IAClC,QAAQ;IACR,YAAY;IACZ,WAAW;IACX,eAAe;IACf,mBAAmB;IACnB,OAAO;AACT;AAEF,MAAM,MAAM,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE;AAC1B,MAAM,OAAO,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE;AACrB,MAAM,KAAK,CAAA,GAAA,sKAAA,CAAA,eAAY,AAAD,EAAE;AAoBT,SAAS;;IACtB,uBAAuB;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,aAAa;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QAAE,OAAO;QAAI,UAAU;IAAG;IACjF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QAAE,MAAM;QAAI,KAAK;IAAG;IAErE,aAAa;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,0BAA0B;IAC1B,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAErD,6DAA6D;IAC7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;2DAAiB;oBACrB,IAAI;wBACF,+BAA+B;wBAC/B,MAAM,eAAe,aAAkB,eAAe,OAAO,oBAAoB;wBAEjF,IAAI,cAAc;4BAChB,MAAM,CAAA,GAAA,4NAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM;wBACpC,OAAO;4BACL,wCAAwC;4BACxC,MAAM,CAAA,GAAA,wNAAA,CAAA,oBAAiB,AAAD,EAAE;wBAC1B;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,wBAAwB;oBACxC;gBACF;;YAEA,6BAA6B;YAC7B,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE;wDAAM,CAAC;oBAC5C,QAAQ;oBACR,aAAa;oBAEb,kDAAkD;oBAClD,IAAI,MAAM;wBACR,cAAc;oBAChB;gBACF;;YAEA;YAEA;4CAAO,IAAM;;QACf;mCAAG,EAAE;IAEL,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,eAAe,OAAO,EAAE,eAAe;gBAAE,UAAU;YAAS;QAC9D;mCAAG;QAAC;KAAS;IAEb,+BAA+B;IAC/B,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,IAAI,SAAS,KAAK,GAAG;YACzC,MAAM,WAAW,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE;YAE9B,IAAI,CAAC,SAAS,MAAM,IAAI;gBACtB,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,SAAS;oBACpB,OAAO,KAAK,KAAK;oBACjB,WAAW,IAAI;oBACf,WAAW,IAAI;gBACjB;YACF,OAAO;gBACL,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,SAAS;oBACpB,WAAW,IAAI;gBACjB,GAAG;oBAAE,OAAO;gBAAK;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB,OAAO;QAC5B,EAAE,cAAc;QAChB,UAAU;YAAE,MAAM;YAAI,KAAK;QAAG;QAE9B,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,QAAQ,EAAE;YACzC,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAA4B,CAAC;YACjE;QACF;QAEA,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YAChC,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAyC,CAAC;YAC9E;QACF;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,qOAAA,CAAA,iCAA8B,AAAD,EAAE,MAAM,SAAS,KAAK,EAAE,SAAS,QAAQ;YAC5E,YAAY;gBAAE,OAAO;gBAAI,UAAU;YAAG;QACxC,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAa,CAAC;QACpD;IACF;IAEA,oBAAoB;IACpB,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAChB,UAAU;YAAE,MAAM;YAAI,KAAK;QAAG;QAE9B,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,QAAQ,EAAE;YACzC,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAA4B,CAAC;YACjE;QACF;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,iOAAA,CAAA,6BAA0B,AAAD,EAAE,MAAM,SAAS,KAAK,EAAE,SAAS,QAAQ;YACxE,YAAY;gBAAE,OAAO;gBAAI,UAAU;YAAG;QACxC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAA4B,CAAC;QACnE;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE;YACd,YAAY,EAAE;YACd,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,uEAAuE;IACvE,MAAM,qBAAqB,OAAO;QAChC,qBAAqB;QACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,OAAO,KAAK,MAAM,KAAK;QAExE,0DAA0D;QAC1D,MAAM,kBAAkB,oBAAoB,MAAM,CAAC,CAAA,UACjD,SAAS,WAAW,GAAG,KAAK,CAAC,KAAK,IAAI,CAAC,CAAA,OACrC,QAAQ,WAAW,GAAG,QAAQ,CAAC,SAAS,KAAK,MAAM,GAAG;QAI1D,kEAAkE;QAClE,MAAM,gBAAgB,SAAS,WAAW;QAE1C,IAAI,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,eAAe;YAC7E,OAAO;QACT;QAEA,IAAI,cAAc,QAAQ,CAAC,aAAa,cAAc,QAAQ,CAAC,SAAS;YACtE,OAAO;QACT;QAEA,IAAI,cAAc,QAAQ,CAAC,YAAY,cAAc,QAAQ,CAAC,WAAW;YACvE,OAAO;QACT;QAEA,IAAI,cAAc,QAAQ,CAAC,aAAa,cAAc,QAAQ,CAAC,YAAY;YACzE,OAAO;QACT;QAEA,2CAA2C;QAC3C,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,OAAO,CAAC,kCAAkC,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE,gBAAgB,MAAM,GAAG,IAAI,mBAAmB,eAAe,CAAC,EAAE,GAAG,GAAG,oEAAoE,EAAE,SAAS,WAAW,GAAG,CAAC,CAAC;QAC3O;QAEA,OAAO,CAAC,6CAA6C,EAAE,SAAS,wTAAwT,CAAC;IAC3X;IAEA,sCAAsC;IACtC,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB,IAAI,MAAM,cAAc;QAE7C,MAAM,eAAe,gBAAgB,IAAI;QACzC,MAAM,aAAa,KAAK,GAAG,GAAG,QAAQ;QAEtC,2BAA2B;QAC3B,MAAM,cAAuB;YAC3B,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,mBAAmB;QACnB,gBAAgB;QAChB,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,KAAK;YAAG,CAAC;QAEvC,IAAI;YACF,gEAAgE;YAChE,gDAAgD;YAChD,oBAAoB;YACpB,qDAAqD;YACrD,2BAA2B;YAC3B,8BAA8B;YAC9B,yBAAyB;YACzB,uEAAuE;YACvE,OAAO;YACP,MAAM;YAEN,MAAM,aAAa,MAAM,mBAAmB;YAE5C,0BAA0B;YAC1B,MAAM,YAAqB;gBACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,QAAQ;gBACR,WAAW,IAAI;YACjB;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;QAE1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,KAAK;gBAA4C,CAAC;QAClF,SAAU;YACR,gBAAgB;YAChB,qCAAqC;YACrC,WAAW,IAAM,iBAAiB,OAAO,EAAE,SAAS;QACtD;IACF;IAEA,qCAAqC;IACrC,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;QAC/C,4CAA4C;QAC5C,IAAI,OAAO,IAAI,EAAE;YACf,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAG,CAAC;QAC1C;IACF;IAEA,2BAA2B;IAC3B,MAAM,gBAAgB,kBACpB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAKxC,gCAAgC;IAChC,MAAM,WAAW,kBACf,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,6LAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;kCAIjC,6LAAC;wBAAK,UAAU,UAAU,cAAc;wBAAgB,WAAU;;0CAChE,6LAAC;;kDACC,6LAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAAiD;;;;;;kDAGlF,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU;wCACV,WAAU;wCACV,aAAY;wCACZ,QAAQ;;;;;;;;;;;;0CAIZ,6LAAC;;kDACC,6LAAC;wCAAM,SAAQ;wCAAW,WAAU;kDAAiD;;;;;;kDAGrF,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,QAAQ;wCACxB,UAAU;wCACV,WAAU;wCACV,aAAY;wCACZ,QAAQ;;;;;;;;;;;;4BAKX,OAAO,IAAI,kBACV,6LAAC;gCAAI,WAAU;0CACZ,OAAO,IAAI;;;;;;0CAKhB,6LAAC;gCACC,MAAK;gCACL,WAAU;0CAET,UAAU,YAAY;;;;;;0CAIzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,MAAK;oCACL,SAAS;wCACP,WAAW,CAAC;wCACZ,UAAU;4CAAE,MAAM;4CAAI,KAAK;wCAAG;wCAC9B,YAAY;4CAAE,OAAO;4CAAI,UAAU;wCAAG;oCACxC;oCACA,WAAU;8CAET,UAAU,mCAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ1D,2BAA2B;IAC3B,MAAM,gBAAgB,CAAC,EAAE,OAAO,EAAwB,iBACtD,6LAAC;YAAI,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,GAAG,gBAAgB,gBAAgB,KAAK,CAAC;sBAC7E,cAAA,6LAAC;gBAAI,WAAW,CAAC,+EAA+E,EAC9F,QAAQ,MAAM,GACV,iEACA,uEACJ;;kCACA,6LAAC;wBAAE,WAAU;kCAA+C,QAAQ,IAAI;;;;;;kCACxE,6LAAC;wBAAE,WAAW,CAAC,aAAa,EAAE,QAAQ,MAAM,GAAG,oBAAoB,mBAAmB;kCACnF,QAAQ,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE;4BAAE,MAAM;4BAAW,QAAQ;wBAAU;;;;;;;;;;;;;;;;;IAMvF,gCAAgC;IAChC,MAAM,gBAAgB,kBACpB,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAW;;;;;;kDAC1B,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,6LAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;0CAI3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAA0C;4CAC7C,MAAM,SAAS;;;;;;;kDAE3B,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;8BAQP,6LAAC;oBAAK,WAAU;;sCAEd,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,MAAM,KAAK,kBACnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAAwC;;;;;;sDAIrD,6LAAC;4CAAI,WAAU;sDACZ;gDACC;gDACA;gDACA;gDACA;6CACD,CAAC,GAAG,CAAC,CAAC,YAAY,sBACjB,6LAAC;oDAEC,SAAS,IAAM,mBAAmB;oDAClC,WAAU;8DAET;mDAJI;;;;;;;;;;;;;;;yDAUb;;wCACG,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;gDAA+B,SAAS;+CAArB,QAAQ,EAAE;;;;;wCAI/B,8BACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;oEAAoD,OAAO;wEAAE,gBAAgB;oEAAO;;;;;;8EACnG,6LAAC;oEAAI,WAAU;oEAAoD,OAAO;wEAAE,gBAAgB;oEAAO;;;;;;;;;;;;sEAErG,6LAAC;4DAAK,WAAU;sEAA0B;;;;;;;;;;;;;;;;;;;;;;;;8CAStD,6LAAC;oCAAI,KAAK;;;;;;;;;;;;wBAIX,OAAO,GAAG,kBACT,6LAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG;;;;;;sCAKf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,KAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CAClD,YAAY;4CACZ,aAAY;4CACZ,WAAU;4CACV,MAAM;4CACN,UAAU;;;;;;;;;;;kDAGd,6LAAC;wCACC,SAAS;wCACT,UAAU,CAAC,gBAAgB,IAAI,MAAM;wCACrC,WAAU;kDAET,6BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;iEAGrC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWvF,oBAAoB;IACpB,IAAI,WAAW;QACb,qBAAO,6LAAC;;;;;IACV;IAEA,IAAI,CAAC,MAAM;QACT,qBAAO,6LAAC;;;;;IACV;IAEA,qBAAO,6LAAC;;;;;AACV;GA5ewB;KAAA", "debugId": null}}]}